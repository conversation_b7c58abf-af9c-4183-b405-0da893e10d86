import { chrom<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Wait for files to be created in a directory with smart polling
 */
async function waitForFileCreation(
  directory: string, 
  expectedCount: number = 1, 
  timeout: number = 3000
): Promise<string[]> {
  const startTime = Date.now();
  let lastFileCount = 0;

  while (Date.now() - startTime < timeout) {
    if (fs.existsSync(directory)) {
      const files = fs.readdirSync(directory).filter(file => file.endsWith('.md'));

      // Log progress if file count changes
      if (files.length !== lastFileCount) {
        console.log(`📁 Found ${files.length} files (expecting ${expectedCount})`);
        lastFileCount = files.length;
      }

      if (files.length >= expectedCount) {
        return files;
      }
    }

    // Use faster polling for quicker detection
    await new Promise(resolve => setTimeout(resolve, 25));
  }

  const actualFiles = fs.existsSync(directory)
    ? fs.readdirSync(directory).filter(file => file.endsWith('.md'))
    : [];

  throw new Error(
    `Expected ${expectedCount} files to be created within ${timeout}ms, ` +
    `but found ${actualFiles.length}: [${actualFiles.join(', ')}]`
  );
}

describe("Ghost Sync - Create New Post E2E Test", function () {
  this.timeout(30 * 1000);

  let browser: Browser;
  let page: Page;
  const articlesDir = path.join(__dirname, '../../tests/vault/Test/articles');

  before(async function () {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    // Plugin should be ready immediately - no wait needed
  });

  after(async function () {
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(async function () {
    // Clear any existing files in the articles directory
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.endsWith('.md')) {
          fs.unlinkSync(path.join(articlesDir, file));
        }
      }
    }
  });

  it("should create a new post file with correct content using command palette", async function () {
    const testTitle = "Test Post Title E2E";

    // Open command palette using keyboard shortcut
    await page.keyboard.down('Meta'); // Cmd on Mac
    await page.keyboard.press('KeyP');
    await page.keyboard.up('Meta');

    // Type the command name and wait for it to appear
    await page.keyboard.type('Ghost Sync: Create new post');

    // Press Enter to execute the command
    await page.keyboard.press('Enter');

    // Wait for the "Enter post title" dialog to appear
    const titleInput = page.locator('input[placeholder="Post title..."]');
    await titleInput.waitFor({ timeout: 3000 });
    console.log("Found post title input field");

    // Click on the input field and type the title
    await titleInput.fill(testTitle);

    console.log(`Typed title: ${testTitle}`);

    // Find and click the "Create" button - no explicit wait needed, click() auto-waits
    const createButton = page.locator('button:has-text("Create")');
    await createButton.click();

    console.log("Clicked Create button, waiting for file creation...");

    // Wait for file to be created
    const files = await waitForFileCreation(articlesDir, 1);

    assert(files.length === 1, `Expected 1 file to be created, but found ${files.length}`);

    const createdFile = files[0];
    const expectedFileName = `${testTitle.toLowerCase().replace(/\s+/g, '-')}.md`;

    assert(createdFile === expectedFileName,
      `Expected file name "${expectedFileName}", but got "${createdFile}"`);

    // Verify the file content
    const filePath = path.join(articlesDir, createdFile);
    const fileContent = fs.readFileSync(filePath, 'utf8');

    // Check that the file contains the expected frontmatter
    assert(fileContent.includes(`Title: "${testTitle}"`),
      'File should contain the correct title in frontmatter');
    assert(fileContent.includes('Status: "draft"'),
      'File should have Status: "draft"');
    assert(fileContent.includes('Featured Image: null'),
      'File should have Featured Image: null');
    assert(fileContent.includes('Newsletter: null'),
      'File should have Newsletter: null');

    // Check that the file is opened in Obsidian
    const activeFile = await page.evaluate(() => {
      return (window as any).app.workspace.getActiveFile()?.name;
    });

    assert(activeFile === createdFile,
      `Expected active file to be "${createdFile}", but got "${activeFile}"`);

    console.log(`✅ Successfully created and verified post: ${createdFile}`);
    console.log(`📄 File content preview:\n${fileContent.substring(0, 200)}...`);
  });

  it("should create a new post using direct command execution", async function () {
    const testTitle = "Direct Command Test Post";

    // Execute the command directly and then handle the dialog
    await page.evaluate(() => {
      console.log('Executing command: ghost-sync:create-new-post');
      (window as any).app.commands.executeCommandById('ghost-sync:create-new-post');
    });

    console.log("Waiting for dialog after direct command execution...");

    // Wait for the input field
    const titleInput = page.locator('input[placeholder="Post title..."]');
    await titleInput.waitFor({ timeout: 3000 });
    console.log("Found input field");

    // Clear any existing text and type the title
    await titleInput.fill(testTitle);

    console.log(`Typed title: ${testTitle}`);

    // Find and click the Create button - no explicit wait needed
    const createButton = page.locator('button:has-text("Create")');
    await createButton.click();

    console.log("Clicked Create button");

    // Wait for file to be created
    const files = await waitForFileCreation(articlesDir, 1);

    console.log("Files found in articles directory:", files);
    console.log("Expected file name:", `${testTitle.toLowerCase().replace(/\s+/g, '-')}.md`);

    assert(files.length === 1, `Expected 1 file to be created, but found ${files.length}. Files: ${files.join(', ')}`);

    const createdFile = files[0];
    const expectedFileName = `${testTitle.toLowerCase().replace(/\s+/g, '-')}.md`;

    assert(createdFile === expectedFileName,
      `Expected file name "${expectedFileName}", but got "${createdFile}"`);

    // Verify the file content
    const filePath = path.join(articlesDir, createdFile);
    const fileContent = fs.readFileSync(filePath, 'utf8');

    // Check that the file contains the expected frontmatter
    assert(fileContent.includes(`Title: "${testTitle}"`),
      'File should contain the correct title in frontmatter');

    console.log(`✅ Successfully created post via direct command: ${createdFile}`);
  });
});
